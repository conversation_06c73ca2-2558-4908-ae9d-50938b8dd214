"use client";

import { useRef, useState, useEffect } from 'react';
import AnimatedFixedTitle from './AnimatedFixedTitle';
import ProcessCard from './ProcessCard';

const Process = () => {
  const sectionRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [titleVisible, setTitleVisible] = useState(false);
  const [scrollEffects, setScrollEffects] = useState({
    opacity: 1,
    blur: 0,
    scale: 1
  });
  const [processScrollProgress, setProcessScrollProgress] = useState(0);
  const [isProcessComplete, setIsProcessComplete] = useState(false);
  const [normalScrollProgress, setNormalScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const processRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Get Projects section (previous section) to determine when to show Process title
      const projectsSection = document.querySelector('[data-section="projects"]');
      if (!projectsSection) return;

      const projectsRect = projectsSection.getBoundingClientRect();
      const projectsBottom = projectsRect.bottom;
      const projectsTop = projectsRect.top;

      // Show title earlier - adjust this value to control when title disappears on scroll up
      // Higher values = disappears earlier, Lower values = disappears later
      const triggerPoint = windowHeight * 0.2; // Try 0.6 (60%) to make it disappear earlier
      const projectsAlmostGone = projectsBottom <= triggerPoint;
      const projectsStartedScrolling = projectsTop <= windowHeight * 0.8;

      // Title appears when Projects is almost gone - SAME LOGIC AS PROJECTS
      const shouldShowTitle = projectsAlmostGone && projectsStartedScrolling;

      // Show animation when scrolling down and condition is met
      if (shouldShowTitle && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setTitleVisible(true);
      }

      // Hide animation when scrolling back up past the same trigger point
      if (!shouldShowTitle && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setTitleVisible(false);
      }

      // Calculate scroll-driven effects when title is visible
      if (titleVisible) {
        // Calculate how far we've scrolled into the Process section
        const processTop = processRect.top;
        const processHeight = processRect.height;

        // Start process animations when Process section reaches top of viewport
        const triggerOffset = windowHeight * 0.1;
        if (processTop <= triggerOffset) {
          // SIMPLE CALCULATION - Cards appear when title starts fading
          const scrolledIntoProcess = Math.abs(processTop - triggerOffset);

          // Use the full section height for 1:1 scroll mapping
          const titleFadeDistance = windowHeight * 4; // Match the 400vh section height
          const rawScrollProgress = Math.min(1, scrolledIntoProcess / titleFadeDistance);

          // Add a delay buffer - title stays crisp for the first 10% of scroll progress
          const delayBuffer = 0.10;
          const adjustedScrollProgress = Math.max(0, (rawScrollProgress - delayBuffer) / (1 - delayBuffer));

          // Apply scroll-driven effects to title
          const opacity = adjustedScrollProgress > 0 ? Math.max(0, 1 - (adjustedScrollProgress * 3)) : 1;
          const blur = adjustedScrollProgress * 10;
          const scale = adjustedScrollProgress > 0 ? Math.max(0.8, 1 - (adjustedScrollProgress * 0.5)) : 1;

          setScrollEffects({ opacity, blur, scale });

          // Cards use direct scroll progress - no adjustments
          setProcessScrollProgress(rawScrollProgress);

          // Simple completion - section ends when cards are done
          setIsProcessComplete(rawScrollProgress >= 1);
          setNormalScrollProgress(Math.max(0, rawScrollProgress - 1));
        } else {
          // Reset effects when Process section hasn't reached the trigger point yet
          setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
          setProcessScrollProgress(0);
          setIsProcessComplete(false);
          setNormalScrollProgress(0);
        }
      } else {
        // Title not visible yet - reset everything
        setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
        setProcessScrollProgress(0);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn, titleVisible]);

  // Process steps data
  const processSteps = [
    {
      id: 1,
      number: "01",
      title: "Initial Proposal",
      description: "We discuss your vision and I provide a detailed proposal with timeline and pricing."
    },
    {
      id: 2,
      number: "02",
      title: "Design & Planning",
      description: "I create wireframes and mockups, then plan the technical architecture for your project."
    },
    {
      id: 3,
      number: "03",
      title: "Development",
      description: "I build your project using modern technologies, keeping you updated throughout the process."
    },
    {
      id: 4,
      number: "04",
      title: "Launch & Support",
      description: "I deploy your project and provide ongoing support to ensure everything runs smoothly."
    }
  ];

  return (
    <>
      {/* Fixed/Centered Title with scroll-driven effects - LOWER Z-INDEX */}
      <div
        className="fixed inset-0 flex items-center justify-center pointer-events-none z-0"
        style={{
          transform: isProcessComplete ? `translateY(${-normalScrollProgress * 100}vh)` : 'translateY(0)'
        }}
      >
        <AnimatedFixedTitle
          title="Process"
          titleVisible={titleVisible}
          scrollEffects={scrollEffects}
          className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
          containerClassName=""
        />
      </div>

      {/* Process Section */}
      <section
        ref={sectionRef}
        data-section="process"
        className="bg-background relative z-10"
        style={{ height: '400vh' }} // 4x viewport height = 1vh per card
      >
        {/* Process Cards Container */}
        <div className="sticky top-0 h-screen w-full overflow-hidden">
          {/* Process Cards with CSS mask for fade effect */}
          <div
            className="h-full flex items-center justify-center relative"
            style={{
              mask: `linear-gradient(to top,
                transparent 5%,
                black 25%,
                black 100%)`,
              WebkitMask: `linear-gradient(to top,
                transparent 5%,
                black 25%,
                black 100%)`
            }}
          >
            <ProcessCard
              scrollProgress={processScrollProgress}
              processSteps={processSteps}
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default Process;
