"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import BurgerMenuIcon from "../../public/web-icons/burger-menu-svgrepo-com-white.svg";

function DesktopNavbar({ isVisible }) {
  return (
    <motion.nav
      className="hidden md:flex fixed top-0 left-0 right-0 z-50 w-3/4 mx-auto rounded-lg shadow-md py-4 px-6 items-center justify-between h-16 bg-primary"
      initial={{ y: 0 }}
      animate={{ y: isVisible ? 0 : -100 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Name on the left */}
      <div className="text-secondary text-xl font-heading font-bold whitespace-nowrap mr-8">
        Cretu Teodor
      </div>

      {/* Menu items on the right */}
      <div className="flex items-center space-x-6">
        <a
          href="#"
          className="text-secondary text-lg font-semibold hover:text-accent transition-colors"
        >
          Services
        </a>
        <a
          href="#"
          className="text-secondary text-lg font-semibold hover:text-accent transition-colors"
        >
          Projects
        </a>
        <a
          href="#"
          className="text-secondary text-lg font-semibold hover:text-accent transition-colors"
        >
          View Resume
        </a>
        <a
          href="#"
          className="text-secondary text-lg font-semibold hover:text-accent transition-colors"
        >
          Contact
        </a>
        <span className="text-secondary/50 text-lg">|</span>
        <a
          href="#"
          className="bg-secondary text-primary px-4 py-2 rounded-full font-semibold hover:bg-secondary/90 hover:scale-105 transition-all duration-300"
        >
          Request Project
        </a>
      </div>
    </motion.nav>
  );
}

function MobileNavbar({ isVisible }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <motion.nav
      className="flex md:hidden fixed top-0 left-0 right-0 z-50 py-4 items-center justify-start h-16 bg-primary"
      initial={{ y: 0 }}
      animate={{ y: isVisible ? 0 : -100 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      <div className="relative flex items-center w-full px-2">
        {/* Burger menu on the far left edge */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="text-secondary focus:outline-none z-20 p-2"
          aria-label="Toggle menu"
        >
          <img src={BurgerMenuIcon.src} alt="Menu" className="w-8 h-8" />
        </button>

        {/* Name centered */}
        <div className="absolute left-1/2 transform -translate-x-1/2 text-secondary text-xl font-heading font-bold whitespace-nowrap pointer-events-none">
          Cretu Teodor
        </div>
      </div>

      {/* Dropdown menu */}
      {isOpen && (
        <motion.div
          className="absolute top-full left-0 w-full p-4 shadow-md flex flex-col space-y-4 z-40 bg-primary"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
        >
          <a
            href="#"
            className="text-foreground text-lg font-semibold hover:text-accent transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Home
          </a>
          <a
            href="#"
            className="text-foreground text-lg font-semibold hover:text-accent transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Services
          </a>
          <a
            href="#"
            className="text-foreground text-lg font-semibold hover:text-accent transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Projects
          </a>
          <a
            href="#"
            className="text-foreground text-lg font-semibold hover:text-accent transition-colors"
            onClick={() => setIsOpen(false)}
          >
            View Resume
          </a>
          <a
            href="#"
            className="text-foreground text-lg font-semibold hover:text-accent transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Contact
          </a>
          <a
            href="#"
            className="bg-secondary text-primary px-4 py-2 rounded-full font-semibold hover:bg-secondary/90 transition-colors"
            onClick={() => setIsOpen(false)}
          >
            Request Project
          </a>
        </motion.div>
      )}
    </motion.nav>
  );
}

export default function Navbar() {
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      // Show navbar when scrolling up or at the top
      if (currentScrollY < lastScrollY || currentScrollY < 10) {
        setIsVisible(true);
      } 
      // Hide navbar when scrolling down (but not if we're at the very top)
      else if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [lastScrollY]);

  return (
    <>
      <MobileNavbar isVisible={isVisible} />
      <DesktopNavbar isVisible={isVisible} />
    </>
  );
}
