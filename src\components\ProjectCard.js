"use client";

import { motion } from 'framer-motion';

const ProjectCard = ({ scrollProgress, projects }) => {
  // Calculate which project we're currently showing
  const projectCount = projects.length;
  const segmentSize = 1 / projectCount;

  // Handle completion state - when scrollProgress is 1.0, show the last project fully
  let currentProjectIndex, segmentProgress;

  if (scrollProgress >= 1.0) {
    // Animation complete - show last project at final position
    currentProjectIndex = projectCount - 1;
    segmentProgress = 1.0;
  } else {
    currentProjectIndex = Math.floor(scrollProgress / segmentSize);
    segmentProgress = (scrollProgress % segmentSize) / segmentSize; // 0-1 within current segment

    // Ensure we don't exceed array bounds
    currentProjectIndex = Math.min(currentProjectIndex, projectCount - 1);
  }

  const activeProjectIndex = currentProjectIndex;

  return (
    <div className="w-full h-full relative bg-background flex items-center justify-center p-4">
      {/* Render all project cards stacked */}
      {projects.map((project, index) => {
        // Calculate if this card should be visible
        let cardOpacity = 0;
        let cardTranslateX = 200; // Start completely off-screen (200% to the right)

        if (index < activeProjectIndex) {
          // Previous cards - fully visible and in position
          cardOpacity = 1;
          cardTranslateX = 0;
        } else if (index === activeProjectIndex) {
          // Current card - sliding in based on segment progress
          cardOpacity = 1;
          // Card starts moving only when its segment begins, slides from 200% to 0%
          cardTranslateX = Math.max(0, 200 - (segmentProgress * 200));
        } else if (index === activeProjectIndex + 1 && segmentProgress > 0.8) {
          // Next card - only show subtle preview when current card is almost done (last 20% of segment)
          const previewProgress = (segmentProgress - 0.8) / 0.2; // 0-1 for the last 20%
          cardOpacity = previewProgress * 0.2; // Very subtle opacity
          cardTranslateX = 200 - (previewProgress * 25); // Only peek 25% into view
        }

        return (
          <div
            key={project.id}
            className="absolute bg-secondary rounded-3xl border border-primary/20 shadow-lg"
            style={{
              opacity: cardOpacity,
              transform: `translateX(${cardTranslateX}%)`,
              zIndex: index,
              // Responsive sizing within 90% container: use as much space as possible while maintaining 4:3 aspect ratio
              width: 'min(100%, calc((100vh - 8rem) * 4/3))', // Width constrained by height
              height: 'min(calc(100vw * 0.9 * 0.67 * 3/4), calc(100vh - 8rem))', // Height constrained by container width (90% * 2/3)
              aspectRatio: '4/3'
            }}
          >
            {/* Visual showcase content - removed redundant aspect-[4/3] */}
            <div className="p-8 h-full flex items-center justify-center bg-primary rounded-3xl">
              <div className="text-center">
                {/* Project preview image area */}
                <div className="aspect-square bg-primary/20 rounded-2xl mb-6 flex items-center justify-center overflow-hidden mx-auto max-w-xs">
                  <span className="text-secondary text-lg font-medium">{project.title}</span>
                  {/* Future: Replace with actual project image */}
                  {/* <img src={project.image} alt={project.title} className="w-full h-full object-cover" /> */}
                </div>

                {/* Project showcase details */}
                <div className="space-y-2">
                  <div className="text-sm text-secondary">Live Demo</div>
                  <div className="text-sm text-secondary">Gallery • Screenshots</div>
                </div>

                {/* Project indicator */}
                <div className="mt-4 flex justify-center space-x-2">
                  {projects.map((_, dotIndex) => (
                    <div
                      key={dotIndex}
                      className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                        dotIndex === Math.min(activeProjectIndex, projectCount - 1) ? 'bg-primary' : 'bg-primary/30'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ProjectCard;
