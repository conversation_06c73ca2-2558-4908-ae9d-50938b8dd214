"use client";

const ProcessCard = ({ scrollProgress, processSteps }) => {
  // EASY TO EDIT: Card spawn point configuration
  const CARD_SPAWN_POINT = 40; // vh units - how far below center cards spawn (easy to adjust)

  // Calculate which step we're currently showing
  const stepCount = processSteps.length;
  const segmentSize = 1 / stepCount; // Each card gets equal portion of scroll progress

  // EASY TO EDIT: Delay when cards start appearing (0.0 = immediately, 0.5 = halfway through segment)
  const CARD_APPEARANCE_DELAY = 0.3; // Cards appear 30% into their segment for more breathing room

  // Handle completion state - when scrollProgress is 1.0, show all cards stacked
  let currentStepIndex, segmentProgress;

  if (scrollProgress >= 1.0) {
    // Animation complete - show all cards stacked
    currentStepIndex = stepCount - 1;
    segmentProgress = 1.0;
  } else {
    currentStepIndex = Math.floor(scrollProgress / segmentSize);
    segmentProgress = (scrollProgress % segmentSize) / segmentSize; // 0-1 within current segment

    // Ensure we don't exceed array bounds
    currentStepIndex = Math.min(currentStepIndex, stepCount - 1);
  }

  return (
    <div className="w-full h-full relative flex items-center justify-center">
      {/* Render all process cards */}
      {processSteps.map((step, index) => {
        // Calculate if this card should be visible and its position
        let cardOpacity = 0;
        let cardTranslateY = CARD_SPAWN_POINT; // Start from configurable distance below center
        let cardScale = 0.8; // Start smaller

        if (index < currentStepIndex) {
          // Previous cards - fully visible and centered (no stacking offset)
          cardOpacity = 1;
          cardTranslateY = 0; // At center
          cardScale = 1; // Full size
        } else if (index === currentStepIndex) {
          // Current card - only appears after delay for breathing room
          if (segmentProgress >= CARD_APPEARANCE_DELAY) {
            // Adjust progress to start from 0 when card begins appearing
            const adjustedProgress = (segmentProgress - CARD_APPEARANCE_DELAY) / (1 - CARD_APPEARANCE_DELAY);

            // Opacity: SLOWER fade in over 60% of adjusted progress (was 20%)
            cardOpacity = Math.min(1, adjustedProgress / 0.6);

            // Scale: SLOWER grow from 0.8 to 1.0 over 70% of adjusted progress (was 30%)
            cardScale = Math.min(1, 0.8 + (adjustedProgress / 0.7) * 0.2);

            // Movement: SLOWER continuous movement from spawn point to center
            cardTranslateY = CARD_SPAWN_POINT - (adjustedProgress * CARD_SPAWN_POINT);
          }
          // Before delay: card remains invisible at spawn point
        }
        // Future cards remain invisible (cardOpacity = 0, cardTranslateY = CARD_SPAWN_POINT, cardScale = 0.8)

        return (
          <div
            key={step.id}
            className="absolute bg-primary rounded-2xl border border-secondary/20 shadow-lg p-6"
            style={{
              opacity: cardOpacity,
              transform: `translateY(${cardTranslateY}vh) scale(${cardScale}) translateZ(0)`,
              // Bigger responsive sizing
              width: 'min(450px, 40vw)', // Increased width, max 450px
              height: 'min(280px, 25vh)', // Increased height, max 280px
              zIndex: 20 + index, // Higher z-index for later cards (so they appear on top)
              // NO TRANSITION - natural scroll speed only
              transition: 'none',
              // Add slight shadow for depth
              boxShadow: `0px 4px 20px rgba(0, 0, 0, 0.3)`
            }}
          >
            {/* Horizontal Layout: Number on left, Text on right */}
            <div className="flex items-start gap-6 h-full">
              {/* Step Number - Left Side */}
              <div className="flex-shrink-0 w-16 h-16 bg-accent rounded-full flex items-center justify-center">
                <span className="text-primary text-2xl font-bold font-heading">
                  {step.number}
                </span>
              </div>

              {/* Text Content - Right Side */}
              <div className="flex-1 flex flex-col justify-center">
                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl mb-3">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ProcessCard;
