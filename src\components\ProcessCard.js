"use client";

const ProcessCard = ({ scrollProgress, processSteps }) => {
  // Calculate which step we're currently showing
  const stepCount = processSteps.length;
  const segmentSize = 1 / stepCount; // Each card gets equal portion of scroll progress

  return (
    <div className="w-full h-full relative flex items-center justify-center">
      {/* Render all process cards - they all sit at center like static elements */}
      {processSteps.map((step, index) => {
        // Calculate this card's scroll segment
        const cardStartProgress = index * segmentSize;
        const cardEndProgress = (index + 1) * segmentSize;

        // Card visibility based on scroll progress
        let cardOpacity = 0;
        let cardScale = 0.8;

        if (scrollProgress >= cardStartProgress) {
          if (scrollProgress <= cardEndProgress) {
            // Card is in its active segment
            const segmentProgress = (scrollProgress - cardStartProgress) / segmentSize;

            // Opacity: fade in over first 25% of segment
            cardOpacity = Math.min(1, segmentProgress / 0.25);

            // Scale: grow over first 30% of segment
            cardScale = Math.min(1, 0.8 + (segmentProgress / 0.3) * 0.2);
          } else {
            // Card has passed its segment - stay fully visible
            cardOpacity = 1;
            cardScale = 1;
          }
        }
        // Before card's segment: remains invisible

        return (
          <div
            key={step.id}
            className="absolute bg-primary rounded-2xl border border-secondary/20 shadow-lg p-6"
            style={{
              opacity: cardOpacity,
              transform: `translateY(${cardTranslateY}vh) scale(${cardScale}) translateZ(0)`,
              // Bigger responsive sizing
              width: 'min(450px, 40vw)', // Increased width, max 450px
              height: 'min(280px, 25vh)', // Increased height, max 280px
              zIndex: 20 + index, // Higher z-index for later cards (so they appear on top)
              // NO TRANSITION - natural scroll speed only
              transition: 'none',
              // Add slight shadow for depth
              boxShadow: `0px 4px 20px rgba(0, 0, 0, 0.3)`
            }}
          >
            {/* Horizontal Layout: Number on left, Text on right */}
            <div className="flex items-start gap-6 h-full">
              {/* Step Number - Left Side */}
              <div className="flex-shrink-0 w-16 h-16 bg-accent rounded-full flex items-center justify-center">
                <span className="text-primary text-2xl font-bold font-heading">
                  {step.number}
                </span>
              </div>

              {/* Text Content - Right Side */}
              <div className="flex-1 flex flex-col justify-center">
                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl mb-3">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ProcessCard;
