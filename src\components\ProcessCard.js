"use client";

const ProcessCard = ({ scrollProgress, processSteps }) => {
  // Calculate which step we're currently showing
  const stepCount = processSteps.length;
  const segmentSize = 1 / stepCount; // Each card gets equal portion of scroll progress

  // Handle completion state - when scrollProgress is 1.0, show all cards stacked
  let currentStepIndex, segmentProgress;

  if (scrollProgress >= 1.0) {
    // Animation complete - show all cards stacked
    currentStepIndex = stepCount - 1;
    segmentProgress = 1.0;
  } else {
    currentStepIndex = Math.floor(scrollProgress / segmentSize);
    segmentProgress = (scrollProgress % segmentSize) / segmentSize; // 0-1 within current segment

    // Ensure we don't exceed array bounds
    currentStepIndex = Math.min(currentStepIndex, stepCount - 1);
  }

  return (
    <div className="w-full h-full relative flex items-center justify-center">
      {/* Render all process cards */}
      {processSteps.map((step, index) => {
        // Calculate if this card should be visible and its position
        let cardOpacity = 0;
        let cardTranslateY = 25; // Start from 25vh below center
        let stackOffset = 0; // Offset for stacking effect

        if (index < currentStepIndex) {
          // Previous cards - fully visible and centered
          cardOpacity = 1;
          cardTranslateY = 0; // At center
          stackOffset = 0; // No offset - all cards at same position
        } else if (index === currentStepIndex) {
          // Current card - fast opacity fade while always moving up

          // Fast opacity in first 15% of progress (1-2 scroll steps)
          cardOpacity = Math.min(1, segmentProgress / 0.15);

          // Always moving up from 25vh to center (0vh) throughout entire progress
          cardTranslateY = 25 - (segmentProgress * 25);

          stackOffset = 0; // No offset - all cards at same position
        }
        // Future cards remain invisible (cardOpacity = 0, cardTranslateY = 25)

        return (
          <div
            key={step.id}
            className="absolute bg-primary rounded-2xl border border-secondary/20 shadow-lg p-8"
            style={{
              opacity: cardOpacity,
              transform: `translateY(${cardTranslateY}vh) translateY(${stackOffset}px) translateZ(0)`,
              // Responsive sizing based on viewport - smaller than project cards
              width: 'min(300px, 25vw)', // Responsive width, max 300px
              height: 'min(200px, 20vh)', // Responsive height, max 200px
              zIndex: 20 + index, // Higher z-index for later cards (so they appear on top)
              // NO TRANSITION - natural scroll speed only
              transition: 'none',
              // Add slight shadow for depth
              boxShadow: `0px 4px 20px rgba(0, 0, 0, 0.3)`
            }}
          >
            {/* Card Content */}
            <div className="h-full flex flex-col justify-center text-center">
              {/* Step Number */}
              <div className="text-secondary text-4xl font-bold mb-2">
                {step.number}
              </div>

              {/* Step Title */}
              <h3 className="text-secondary text-lg font-semibold mb-3">
                {step.title}
              </h3>

              {/* Step Description */}
              <p className="text-secondary text-sm leading-relaxed">
                {step.description}
              </p>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ProcessCard;
